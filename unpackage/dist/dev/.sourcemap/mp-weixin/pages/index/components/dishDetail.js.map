{"version": 3, "sources": ["webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?292d", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?26cd", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?7788", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?d422", "uni-app:///pages/index/components/dishDetail.vue", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?e5e2", "webpack:////Users/<USER>/project_code/xiaopaotui/xiaopaotui/pages/index/components/dishDetail.vue?8182"], "names": ["props", "dishDetailes", "type", "default", "openDetailPop", "dishMealData", "methods", "addDishAction", "console", "obj", "item", "redDishAction", "moreNormDataesHandle", "dishClose"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACmN;AACnN,gBAAgB,iNAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAAu1B,CAAgB,wyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCsH32B;EACA;EACAA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;EACA;EACAG;IACA;IACAC;MACAC;MACA;QAAAC;QAAAC;MAAA;IACA;IACAC;MACA;QAAAF;QAAAC;MAAA;IACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxJA;AAAA;AAAA;AAAA;AAA0lD,CAAgB,+7CAAG,EAAC,C;;;;;;;;;;;ACA9mD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/dishDetail.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./dishDetail.vue?vue&type=template&id=0142b43e&scoped=true&\"\nvar renderjs\nimport script from \"./dishDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./dishDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./dishDetail.vue?vue&type=style&index=0&id=0142b43e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0142b43e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/dishDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=template&id=0142b43e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dishDetailes.type == 1 ? _vm.dishDetailes.price.toFixed(2) : null\n  var g1 =\n    _vm.dishDetailes.type == 1\n      ? _vm.dishDetailes.flavors.length === 0 && _vm.dishDetailes.dishNumber > 0\n      : null\n  var g2 = _vm.dishDetailes.type == 1 ? _vm.dishDetailes.flavors.length : null\n  var g3 =\n    _vm.dishDetailes.type == 1\n      ? _vm.dishDetailes.dishNumber === 0 &&\n        _vm.dishDetailes.flavors.length === 0\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=script&lang=js&\"", "<!--选择多规格弹层-->\n<template>\n  <!-- 餐品详情 -->\n  <view class=\"dish_detail_pop\" v-if=\"dishDetailes.type == 1\">\n    <image\n      mode=\"aspectFill\"\n      class=\"div_big_image\"\n      :src=\"dishDetailes.image\"\n    ></image>\n    <view class=\"title\">{{ dishDetailes.name }}</view>\n    <view class=\"desc\">{{ dishDetailes.description }}</view>\n    <view class=\"but_item\">\n      <view class=\"price\">\n        <text class=\"ico\">￥</text>\n        {{ dishDetailes.price.toFixed(2) }}\n      </view>\n      <view\n        class=\"active\"\n        v-if=\"dishDetailes.flavors.length === 0 && dishDetailes.dishNumber > 0\"\n      >\n        <image\n          src=\"../../../static/btn_red.png\"\n          @click=\"redDishAction(dishDetailes, '普通')\"\n          class=\"dish_red\"\n          mode=\"\"\n        ></image>\n        <text class=\"dish_number\">{{ dishDetailes.dishNumber }}</text>\n        <image\n          src=\"../../../static/btn_add.png\"\n          class=\"dish_add\"\n          @click=\"addDishAction(dishDetailes, '普通')\"\n          mode=\"\"\n        ></image>\n      </view>\n\n      <view class=\"active\" v-if=\"dishDetailes.flavors.length > 0\"\n        ><view class=\"dish_card_add\" @click=\"moreNormDataesHandle(dishDetailes)\"\n          >选择规格</view\n        ></view\n      >\n      <view\n        class=\"active\"\n        v-if=\"\n          dishDetailes.dishNumber === 0 && dishDetailes.flavors.length === 0\n        \"\n      >\n        <view class=\"dish_card_add\" @click=\"addDishAction(dishDetailes, '普通')\"\n          >加入购物车</view\n        >\n      </view>\n    </view>\n    <view class=\"close\" @click=\"dishClose\"\n      ><image\n        class=\"close_img\"\n        src=\"../../../static/but_close.png\"\n        mode=\"\"\n      ></image\n    ></view>\n  </view>\n  <!-- end -->\n  <!-- 套餐详情 -->\n  <view class=\"dish_detail_pop\" v-else>\n    <scroll-view class=\"dish_items\" scroll-y=\"true\" scroll-top=\"0rpx\">\n      <view\n        class=\"dish_item\"\n        v-for=\"(item, index) in dishMealData\"\n        :key=\"index\"\n      >\n        <image class=\"div_big_image\" :src=\"item.image\" mode=\"\"></image>\n        <view class=\"title\">\n          {{ item.name }}\n          <text style=\"\">X{{ item.copies }}</text>\n        </view>\n        <view class=\"desc\">{{ item.description }}</view>\n      </view>\n    </scroll-view>\n    <view class=\"but_item\">\n      <view class=\"price\">\n        <text class=\"ico\">￥</text>\n        {{ dishDetailes.price }}\n      </view>\n      <view\n        class=\"active\"\n        v-if=\"dishDetailes.dishNumber && dishDetailes.dishNumber > 0\"\n      >\n        <image\n          src=\"../../../static/btn_red.png\"\n          @click=\"redDishAction(dishDetailes, '普通')\"\n          class=\"dish_red\"\n          mode=\"\"\n        ></image>\n        <text class=\"dish_number\">{{ dishDetailes.dishNumber }}</text>\n        <image\n          src=\"../../../static/btn_add.png\"\n          class=\"dish_add\"\n          @click=\"addDishAction(dishDetailes, '普通')\"\n          mode=\"\"\n        ></image>\n      </view>\n      <view class=\"active\" v-else-if=\"dishDetailes.dishNumber == 0\"\n        ><view\n          class=\"dish_card_add\"\n          @click=\"addDishAction(dishDetailes, '普通')\"\n          >加入购物车</view\n        ></view\n      >\n    </view>\n    <view class=\"close\" @click=\"dishClose\"\n      ><image\n        class=\"close_img\"\n        src=\"../../../static/but_close.png\"\n        mode=\"\"\n      ></image\n    ></view>\n  </view>\n  <!-- end -->\n</template>\n<script>\nexport default {\n  // 获取父级传的数据\n  props: {\n    dishDetailes: {\n      type: Object,\n      default: () => ({}),\n    },\n    openDetailPop: {\n      type: Boolean,\n      default: false,\n    },\n    dishMealData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  methods: {\n    // 加入购物车\n    addDishAction(obj, item) {\n      console.log(obj, item);\n      this.$emit(\"addDishAction\", { obj: obj, item: item });\n    },\n    redDishAction(obj, item) {\n      this.$emit(\"redDishAction\", { obj: obj, item: item });\n    },\n    // 选择规格\n    moreNormDataesHandle(obj) {\n      this.$emit(\"moreNormDataesHandle\", obj);\n    },\n    // 关闭菜单详情\n    dishClose() {\n      this.$emit(\"dishClose\");\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.dish_detail_pop {\n  width: calc(100vw - 160rpx);\n  box-sizing: border-box;\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  padding: 40rpx;\n  transform: translate(-50%, -50%);\n  background: #fff;\n  border-radius: 20rpx;\n  z-index: 9999;\n  .div_big_image {\n    width: 100%;\n    height: 320rpx;\n    border-radius: 10rpx;\n    object-fit: cover;\n  }\n  .title {\n    font-size: 40rpx;\n    line-height: 80rpx;\n    text-align: center;\n    font-weight: bold;\n  }\n  .dish_items {\n    height: 60vh;\n  }\n  .but_item {\n    display: flex;\n    position: relative;\n    flex: 1;\n    .price {\n      text-align: left;\n      color: #e94e3c;\n      line-height: 88rpx;\n      box-sizing: border-box;\n      font-size: 48rpx;\n      font-weight: bold;\n      .ico {\n        font-size: 28rpx;\n      }\n    }\n    .active {\n      position: absolute;\n      right: 0rpx;\n      bottom: 20rpx;\n      display: flex;\n      .dish_add,\n      .dish_red {\n        display: block;\n        width: 72rpx;\n        height: 72rpx;\n      }\n      .dish_number {\n        padding: 0 10rpx;\n        line-height: 72rpx;\n        font-size: 30rpx;\n        font-family: PingFangSC, PingFangSC-Medium;\n        font-weight: 500;\n      }\n      .dish_card_add {\n        width: 200rpx;\n        line-height: 60rpx;\n        text-align: center;\n        font-weight: 500;\n        font-size: 28rpx;\n        opacity: 1;\n        background: #ffc200;\n        border-radius: 30rpx;\n      }\n    }\n  }\n}\n.close {\n  position: absolute;\n  bottom: -180rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  .close_img {\n    width: 88rpx;\n    height: 88rpx;\n  }\n}\n</style>", "import mod from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=style&index=0&id=0142b43e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./dishDetail.vue?vue&type=style&index=0&id=0142b43e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753540046904\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}